// Gmail API Service for Ocean Soul Sparkles
// Integrates with existing Google Cloud email system

import { google } from 'googleapis';

/**
 * Gmail API Service Class
 * Handles OAuth token management and email sending via Gmail API
 */
class GmailAPIService {
  constructor() {
    this.oauth2Client = null;
    this.gmail = null;
    this.initialized = false;
  }

  /**
   * Initialize OAuth2 client
   * @param {Object} tokens - Optional tokens for initialization
   * @returns {Promise<boolean>} - Success status
   */
  async initialize(tokens = null) {
    try {
      if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
        throw new Error('Gmail API OAuth credentials not configured. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in environment variables.');
      }

      this.oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        this.getRedirectUri()
      );

      if (tokens) {
        this.oauth2Client.setCredentials(tokens);
      } else if (process.env.GOOGLE_REFRESH_TOKEN) {
        // Use refresh token from environment
        this.oauth2Client.setCredentials({
          refresh_token: process.env.GOOGLE_REFRESH_TOKEN,
          access_token: process.env.GOOGLE_ACCESS_TOKEN
        });
      }

      this.gmail = google.gmail({ version: 'v1', auth: this.oauth2Client });
      this.initialized = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize Gmail API:', error);
      this.initialized = false;
      return false;
    }
  }

  /**
   * Get OAuth redirect URI based on environment
   * @returns {string} - Redirect URI
   */
  getRedirectUri() {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    return `${baseUrl}/api/auth/gmail/callback`;
  }

  /**
   * Get authorization URL for OAuth flow
   * @returns {string} - Authorization URL
   */
  getAuthUrl() {
    if (!this.oauth2Client) {
      throw new Error('OAuth2 client not initialized');
    }

    const scopes = [
      'https://www.googleapis.com/auth/gmail.send',
      'https://www.googleapis.com/auth/gmail.compose'
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent'
    });
  }

  /**
   * Exchange authorization code for tokens
   * @param {string} code - Authorization code from OAuth callback
   * @returns {Promise<Object>} - Tokens object
   */
  async exchangeCodeForTokens(code) {
    try {
      if (!this.oauth2Client) {
        await this.initialize();
      }

      const { tokens } = await this.oauth2Client.getToken(code);
      this.oauth2Client.setCredentials(tokens);
      
      return {
        success: true,
        tokens,
        expires_at: tokens.expiry_date
      };
    } catch (error) {
      console.error('Failed to exchange code for tokens:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Refresh access token using refresh token
   * @returns {Promise<Object>} - New tokens
   */
  async refreshTokens() {
    try {
      if (!this.oauth2Client) {
        throw new Error('OAuth2 client not initialized');
      }

      const { credentials } = await this.oauth2Client.refreshAccessToken();
      this.oauth2Client.setCredentials(credentials);
      
      return {
        success: true,
        tokens: credentials,
        expires_at: credentials.expiry_date
      };
    } catch (error) {
      console.error('Failed to refresh tokens:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Check if current tokens are valid
   * @returns {Promise<boolean>} - Validity status
   */
  async isTokenValid() {
    try {
      if (!this.oauth2Client || !this.oauth2Client.credentials) {
        return false;
      }

      const accessToken = this.oauth2Client.credentials.access_token;
      const expiryDate = this.oauth2Client.credentials.expiry_date;

      if (!accessToken) {
        return false;
      }

      // Check if token is expired (with 5 minute buffer)
      if (expiryDate && Date.now() > (expiryDate - 300000)) {
        // Try to refresh if we have a refresh token
        if (this.oauth2Client.credentials.refresh_token) {
          const refreshResult = await this.refreshTokens();
          return refreshResult.success;
        }
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error checking token validity:', error);
      return false;
    }
  }

  /**
   * Create email message in proper format for Gmail API
   * @param {Object} params - Email parameters
   * @returns {string} - Base64 encoded email message
   */
  createEmailMessage({ to, subject, text, html, from, fromName }) {
    const fromEmail = from || process.env.GMAIL_FROM_EMAIL || process.env.GMAIL_SMTP_USER;
    const senderName = fromName || process.env.GMAIL_FROM_NAME || 'Ocean Soul Sparkles';
    
    const boundary = `boundary_${Date.now()}_${Math.random()}`;
    const messageParts = [
      `To: ${to}`,
      `From: "${senderName}" <${fromEmail}>`,
      `Subject: ${subject}`,
      'MIME-Version: 1.0',
      `Content-Type: multipart/alternative; boundary="${boundary}"`,
      '',
      `--${boundary}`,
      'Content-Type: text/plain; charset=UTF-8',
      '',
      text,
      ''
    ];

    if (html) {
      messageParts.push(
        `--${boundary}`,
        'Content-Type: text/html; charset=UTF-8',
        '',
        html,
        ''
      );
    }

    messageParts.push(`--${boundary}--`);

    const message = messageParts.join('\n');
    return Buffer.from(message).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  }

  /**
   * Send email using Gmail API
   * @param {Object} params - Email parameters
   * @returns {Promise<Object>} - Result of the operation
   */
  async sendEmail({ to, subject, text, html, from, fromName }) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      if (!this.initialized) {
        throw new Error('Gmail API not properly initialized');
      }

      // Check token validity
      const isValid = await this.isTokenValid();
      if (!isValid) {
        throw new Error('Gmail API tokens are invalid or expired. Re-authorization required.');
      }

      const rawMessage = this.createEmailMessage({ to, subject, text, html, from, fromName });

      const response = await this.gmail.users.messages.send({
        userId: 'me',
        requestBody: {
          raw: rawMessage
        }
      });

      return {
        success: true,
        messageId: response.data.id,
        service: 'gmail-api',
        recipient: to,
        details: {
          gmail_message_id: response.data.id,
          thread_id: response.data.threadId
        }
      };
    } catch (error) {
      console.error('Error sending email via Gmail API:', error);
      
      // Provide specific error handling for common issues
      let errorMessage = error.message;
      if (error.code === 401) {
        errorMessage = 'Gmail API authorization expired. Please re-authorize.';
      } else if (error.code === 403) {
        errorMessage = 'Gmail API access forbidden. Check scopes and permissions.';
      } else if (error.code === 429) {
        errorMessage = 'Gmail API rate limit exceeded. Please try again later.';
      }

      return {
        success: false,
        error: errorMessage,
        service: 'gmail-api',
        code: error.code
      };
    }
  }

  /**
   * Test Gmail API connection
   * @returns {Promise<Object>} - Connection test result
   */
  async testConnection() {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      if (!this.initialized) {
        return {
          success: false,
          message: 'Gmail API not properly initialized',
          details: { error: 'Initialization failed' }
        };
      }

      const isValid = await this.isTokenValid();
      if (!isValid) {
        return {
          success: false,
          message: 'Gmail API tokens are invalid or expired',
          details: { 
            error: 'Token validation failed',
            auth_url: this.getAuthUrl()
          }
        };
      }

      // Test API access by getting user profile
      const profile = await this.gmail.users.getProfile({ userId: 'me' });

      return {
        success: true,
        message: 'Gmail API connection successful',
        details: {
          email: profile.data.emailAddress,
          messages_total: profile.data.messagesTotal,
          threads_total: profile.data.threadsTotal
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Gmail API connection failed',
        details: { 
          error: error.message,
          code: error.code,
          auth_url: this.oauth2Client ? this.getAuthUrl() : null
        }
      };
    }
  }
}

// Create singleton instance
const gmailAPI = new GmailAPIService();

// Export functions for compatibility with existing email system
export const sendGmailAPIEmail = async (params) => {
  return await gmailAPI.sendEmail(params);
};

export const testGmailAPIConnection = async () => {
  return await gmailAPI.testConnection();
};

export const getGmailAuthUrl = () => {
  return gmailAPI.getAuthUrl();
};

export const exchangeGmailCode = async (code) => {
  return await gmailAPI.exchangeCodeForTokens(code);
};

export const refreshGmailTokens = async () => {
  return await gmailAPI.refreshTokens();
};

export const initializeGmailAPI = async (tokens = null) => {
  return await gmailAPI.initialize(tokens);
};

export default gmailAPI;
