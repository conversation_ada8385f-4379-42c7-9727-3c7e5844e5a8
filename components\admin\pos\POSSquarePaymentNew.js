import { useState, useCallback } from 'react'
import { PaymentForm, CreditCard } from 'react-square-web-payments-sdk'
import styles from '@/styles/admin/POS.module.css'

/**
 * POSSquarePaymentNew component using official React Square Web Payments SDK
 * This replaces the complex DOM manipulation approach with React-native components
 *
 * @param {Object} props - Component props
 * @param {number} props.amount - Amount to charge
 * @param {string} props.currency - Currency code (default: AUD)
 * @param {Function} props.onSuccess - Function to call on successful payment
 * @param {Function} props.onError - Function to call on payment error
 * @param {Object} props.orderDetails - Order details for Square
 * @returns {JSX.Element}
 */
export default function POSSquarePaymentNew({
  amount,
  currency = 'AUD',
  onSuccess,
  onError,
  orderDetails = {}
}) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')

  // Get Square configuration from environment
  const appId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID
  const locationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID

  // Handle successful card tokenization
  const handleCardTokenizeResponseReceived = useCallback(async (token, buyer) => {
    if (!token) {
      const error = new Error('No payment token received')
      setErrorMessage(error.message)
      onError(error)
      return
    }

    setIsProcessing(true)
    setErrorMessage('')

    try {
      console.log('✅ Payment tokenized successfully:', token)
      
      // Enable payment operation protection
      const { startPOSPaymentOperation } = await import('@/lib/pos-auth-protection')
      startPOSPaymentOperation()

      // Process payment with the token
      const paymentResponse = await processPayment(token)

      if (paymentResponse.success) {
        onSuccess({
          paymentId: paymentResponse.paymentId,
          paymentStatus: 'COMPLETED',
          paymentDetails: {
            token: token,
            amount: amount,
            currency: currency,
            transactionId: paymentResponse.transactionId
          }
        })
      } else {
        throw new Error(paymentResponse.error || 'Payment processing failed')
      }
    } catch (error) {
      console.error('Payment processing error:', error)
      setErrorMessage(error.message || 'Payment failed. Please try again.')
      onError(error)
    } finally {
      setIsProcessing(false)

      try {
        const { endPOSPaymentOperation } = await import('@/lib/pos-auth-protection')
        endPOSPaymentOperation()
      } catch (protectionError) {
        console.warn('Error ending POS payment protection:', protectionError)
      }
    }
  }, [amount, currency, onSuccess, onError])

  // Create verification details for Strong Customer Authentication
  const createVerificationDetails = useCallback(() => {
    return {
      amount: amount.toString(),
      currencyCode: currency,
      intent: 'CHARGE',
      // Minimal billing contact for Australian business
      billingContact: {
        givenName: 'Customer',
        familyName: 'Customer',
        countryCode: 'AU'
      },
      customerInitiated: true,
      sellerKeyedIn: true // POS terminal entry
    }
  }, [amount, currency])

  // Process payment using Square API
  const processPayment = async (token) => {
    console.log('🔄 Processing payment with token...')

    try {
      const response = await fetch('/api/admin/pos/process-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({
          sourceId: token,
          amountMoney: {
            amount: Math.round(amount * 100), // Convert to cents
            currency: currency
          },
          orderDetails: orderDetails
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Payment failed: ${response.status}`)
      }

      const result = await response.json()
      console.log('✅ Payment processed successfully:', result)
      return result
    } catch (error) {
      console.error('Payment API error:', error)
      throw error
    }
  }

  // Handle payment form errors
  const handlePaymentFormError = useCallback((error) => {
    console.error('Payment form error:', error)
    setErrorMessage(error.message || 'Payment form error occurred')
    onError(error)
  }, [onError])

  // Validate configuration
  if (!appId || !locationId) {
    return (
      <div className={styles.squarePaymentContainer}>
        <div className={styles.paymentError}>
          <span className={styles.errorIcon}>⚠️</span>
          <div className={styles.errorContent}>
            <div className={styles.errorText}>
              Square configuration missing. Please check environment variables.
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.squarePaymentContainer}>
      <div className={styles.paymentFormHeader}>
        <h4>Card Payment</h4>
        <div className={styles.paymentAmount}>
          Amount: <span>${parseFloat(amount || 0).toFixed(2)} {currency}</span>
        </div>
      </div>

      {errorMessage && (
        <div className={styles.paymentError}>
          <span className={styles.errorIcon}>⚠️</span>
          <div className={styles.errorContent}>
            <div className={styles.errorText}>{errorMessage}</div>
          </div>
        </div>
      )}

      <div className={styles.cardFormContainer}>
        <PaymentForm
          applicationId={appId}
          locationId={locationId}
          cardTokenizeResponseReceived={handleCardTokenizeResponseReceived}
          createVerificationDetails={createVerificationDetails}
          onError={handlePaymentFormError}
        >
          <CreditCard
            style={{
              input: {
                fontSize: '16px',
                fontFamily: 'Arial, sans-serif',
                color: '#333'
              },
              'input::placeholder': {
                color: '#999'
              },
              '.message-text': {
                color: '#d32f2f'
              }
            }}
            disabled={isProcessing}
          />
        </PaymentForm>
      </div>

      {isProcessing && (
        <div className={styles.processingOverlay}>
          <div className={styles.processingContent}>
            <div className={styles.loadingSpinner}></div>
            <p>Processing payment...</p>
          </div>
        </div>
      )}

      <div className={styles.paymentActions}>
        <div className={styles.paymentInfo}>
          <p>Enter your card details above and the payment will be processed automatically.</p>
          <p className={styles.securityNote}>
            🔒 Your payment information is secure and encrypted.
          </p>
        </div>
      </div>
    </div>
  )
}
